import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Document,
  DocumentUpdate,
} from '../models';
import {DocumentRepository} from '../repositories';

export class DocumentDocumentUpdateController {
  constructor(
    @repository(DocumentRepository) protected documentRepository: DocumentRepository,
  ) { }

  @get('/documents/{id}/document-updates', {
    responses: {
      '200': {
        description: 'Array of Document has many DocumentUpdate',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DocumentUpdate)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<DocumentUpdate>,
  ): Promise<DocumentUpdate[]> {
    return this.documentRepository.documentUpdates(id).find(filter);
  }

  @post('/documents/{id}/document-updates', {
    responses: {
      '200': {
        description: 'Document model instance',
        content: {'application/json': {schema: getModelSchemaRef(DocumentUpdate)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Document.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentUpdate, {
            title: 'NewDocumentUpdateInDocument',
            exclude: ['id'],
            optional: ['documentId']
          }),
        },
      },
    }) documentUpdate: Omit<DocumentUpdate, 'id'>,
  ): Promise<DocumentUpdate> {
    return this.documentRepository.documentUpdates(id).create(documentUpdate);
  }

  @patch('/documents/{id}/document-updates', {
    responses: {
      '200': {
        description: 'Document.DocumentUpdate PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentUpdate, {partial: true}),
        },
      },
    })
    documentUpdate: Partial<DocumentUpdate>,
    @param.query.object('where', getWhereSchemaFor(DocumentUpdate)) where?: Where<DocumentUpdate>,
  ): Promise<Count> {
    return this.documentRepository.documentUpdates(id).patch(documentUpdate, where);
  }

  @del('/documents/{id}/document-updates', {
    responses: {
      '200': {
        description: 'Document.DocumentUpdate DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(DocumentUpdate)) where?: Where<DocumentUpdate>,
  ): Promise<Count> {
    return this.documentRepository.documentUpdates(id).delete(where);
  }
}
