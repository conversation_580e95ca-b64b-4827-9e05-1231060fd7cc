import {
  repository,
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
} from '@loopback/rest';
import {
  Document,
  DropdownItems,
} from '../models';
import {DocumentRepository} from '../repositories';

export class DocumentDropdownItemsController {
  constructor(
    @repository(DocumentRepository)
    public documentRepository: DocumentRepository,
  ) { }

  @get('/documents/{id}/dropdown-items', {
    responses: {
      '200': {
        description: 'DropdownItems belonging to Document',
        content: {
          'application/json': {
            schema: getModelSchemaRef(DropdownItems),
          },
        },
      },
    },
  })
  async getDropdownItems(
    @param.path.string('id') id: typeof Document.prototype.id,
  ): Promise<DropdownItems> {
    return this.documentRepository.documentCategory(id);
  }
}
