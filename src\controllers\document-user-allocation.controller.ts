import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {DocumentUserAllocation} from '../models';
import {DocumentUserAllocationRepository} from '../repositories';

export class DocumentUserAllocationController {
  constructor(
    @repository(DocumentUserAllocationRepository)
    public documentUserAllocationRepository : DocumentUserAllocationRepository,
  ) {}

  @post('/document-user-allocations')
  @response(200, {
    description: 'DocumentUserAllocation model instance',
    content: {'application/json': {schema: getModelSchemaRef(DocumentUserAllocation)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentUserAllocation, {
            title: 'NewDocumentUserAllocation',
            exclude: ['id'],
          }),
        },
      },
    })
    documentUserAllocation: Omit<DocumentUserAllocation, 'id'>,
  ): Promise<DocumentUserAllocation> {
    return this.documentUserAllocationRepository.create(documentUserAllocation);
  }

  @get('/document-user-allocations/count')
  @response(200, {
    description: 'DocumentUserAllocation model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DocumentUserAllocation) where?: Where<DocumentUserAllocation>,
  ): Promise<Count> {
    return this.documentUserAllocationRepository.count(where);
  }

  @get('/document-user-allocations')
  @response(200, {
    description: 'Array of DocumentUserAllocation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DocumentUserAllocation, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DocumentUserAllocation) filter?: Filter<DocumentUserAllocation>,
  ): Promise<DocumentUserAllocation[]> {
    return this.documentUserAllocationRepository.find(filter);
  }

  @patch('/document-user-allocations')
  @response(200, {
    description: 'DocumentUserAllocation PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentUserAllocation, {partial: true}),
        },
      },
    })
    documentUserAllocation: DocumentUserAllocation,
    @param.where(DocumentUserAllocation) where?: Where<DocumentUserAllocation>,
  ): Promise<Count> {
    return this.documentUserAllocationRepository.updateAll(documentUserAllocation, where);
  }

  @get('/document-user-allocations/{id}')
  @response(200, {
    description: 'DocumentUserAllocation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DocumentUserAllocation, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(DocumentUserAllocation, {exclude: 'where'}) filter?: FilterExcludingWhere<DocumentUserAllocation>
  ): Promise<DocumentUserAllocation> {
    return this.documentUserAllocationRepository.findById(id, filter);
  }

  @patch('/document-user-allocations/{id}')
  @response(204, {
    description: 'DocumentUserAllocation PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentUserAllocation, {partial: true}),
        },
      },
    })
    documentUserAllocation: DocumentUserAllocation,
  ): Promise<void> {
    await this.documentUserAllocationRepository.updateById(id, documentUserAllocation);
  }

  @put('/document-user-allocations/{id}')
  @response(204, {
    description: 'DocumentUserAllocation PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() documentUserAllocation: DocumentUserAllocation,
  ): Promise<void> {
    await this.documentUserAllocationRepository.replaceById(id, documentUserAllocation);
  }

  @del('/document-user-allocations/{id}')
  @response(204, {
    description: 'DocumentUserAllocation DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.documentUserAllocationRepository.deleteById(id);
  }
}
