import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, UserProfile} from '@loopback/security';
import {v4 as uuidv4} from 'uuid';
import {Action, Inspection, TaskSubmitData} from '../models';
import {ActionRepository, InspectionRepository, ServiceRepository, UserRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {ChecklistItem} from '../types/CheckPoint';
// Define interfaces for request data


interface PostAction {
  actionToBeTaken: string;
  dueDate?: string;
  uploads?: string[];
  assignee: string;
}

interface RequestData {
  checklist?: ChecklistItem[];
  postActions?: PostAction[];
}

// interface TaskSubmitData {
//   status?: 'Returned' | 'Completed';
//   reviewerComments?: string;
//   comments?: string;
//   reviewerId?: string;
//   actionTaken?: string;
//   evidence?: string[];
// }

const SERVICE_NAME = 'INS';

@authenticate('cognito-jwt')
export class InspectionController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(ServiceRepository)
    public serviceRepository: ServiceRepository,
    @inject('services.SqsService')
    private sqsService: SqsService,
  ) { }

  @post('/inspections')
  @response(200, {
    description: 'Inspection model instance',
    content: {'application/json': {schema: getModelSchemaRef(Inspection)}},
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, {
            title: 'NewInspection',
            exclude: ['id'],
          }),
        },
      },
    })
    inspection: Omit<Inspection, 'id'>,
  ): Promise<Inspection> {

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const count = await this.inspectionRepository.count();

    inspection.maskId = `${SERVICE_NAME}-${year}${month}${day}-${count.count + 1}`;
    inspection.assignedById = user.id ?? '';
    inspection.status = 'Scheduled';
    const inspectionData = await this.inspectionRepository.create(inspection);
    const actions: Partial<Action> =
    {
      application: SERVICE_NAME,
      actionType: 'conduct_inspection',
      actionToBeTaken: 'Conduct Inspection',
      description: 'Conduct Inspection',
      maskId: inspection.maskId,
      trackId: uuidv4(), // Generate unique id
      sequence: '1',
      prefix: 'INS',
      applicationId: inspectionData.id,
      dueDate: inspection.scheduledDate,
      objectId: inspectionData.id,
      submittedById: user.id,
      assignedToId: [inspection.inspectorId],
      submitURL: '/inspection-task-submit',
      status: 'Initiated',
      serviceId: service.id,

    };


    // Insert into actionRepository
    await this.actionRepository.create(actions);

    // Send notification to the inspector
    if (inspection.inspectorId) {
      const inspector = await this.userRepository.findById(inspection.inspectorId);
      if (inspector && inspector.email) {
        const mailSubject = `New Inspection Assigned - ${inspection.maskId}`;
        const mailBody = `
          <h4>Dear ${inspector.firstName ?? 'User'},</h4>
          <p>You have been assigned to conduct an inspection with ID ${inspection.maskId}.</p>
          <p><strong>Scheduled Date:</strong> ${inspection.scheduledDate ? new Date(inspection.scheduledDate).toLocaleDateString() : 'Not specified'}</p>
          <p><strong>Inspection Category:</strong> ${inspection.inspectionCategory ?? 'Not specified'}</p>
          <p>Please complete the inspection by the scheduled date.</p>
          <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
        `;
        await this.sqsService.sendMessage(inspector, mailSubject, mailBody);
      }
    }

    return inspectionData;
  }

  @patch('/inspection-task-submit/{actionId}')
  @response(204, {
    description: 'Inspection Task PATCH success',
  })
  async submitTaskByActionId(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TaskSubmitData, {partial: true}),
        },
      },
    })
    taskData: TaskSubmitData, // Removed optional marker
  ): Promise<void> {


    // Validate that taskData is provided
    if (!taskData) {
      throw new Error('Data is required for the patch request');
    }

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})
    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const inspectionData = await this.inspectionRepository.findById(actionData.applicationId)
    if (!inspectionData) {
      throw new Error('No Inspection Data Found')
    }


    switch (actionData.actionType) {
      case 'perform_task': {
        // Find a supervisor or manager to review the task
        // For now, we'll use the assignedById from the inspection data as the reviewer
        const actions: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'verify_task',
          actionTaken: taskData.actionTaken ?? '',
          evidence: taskData.evidence ?? [],
          actionToBeTaken: actionData.actionToBeTaken,
          description: actionData.description,
          maskId: inspectionData.maskId,
          trackId: actionData.trackId,
          uploads: actionData.uploads,
          sequence: '1',
          prefix: actionData.prefix,
          applicationId: inspectionData.id,
          objectId: inspectionData.id,
          dueDate: inspectionData.dueDate,
          submittedById: user.id,
          assignedToId: [taskData.reviewerId ?? ''], // Using the assigned by ID as reviewer
          submitURL: '/inspection-task-submit',
          status: 'Initiated',
          serviceId: service.id,
          counter: actionData.counter ?? 0
        };

        await this.actionRepository.updateById(actionId, {actionTaken: taskData.actionTaken ?? '', evidence: taskData.evidence ?? []});
        // Insert into actionRepository
        await this.actionRepository.create(actions);

        // Send notification to the reviewer
        if (taskData.reviewerId) {
          const reviewer = await this.userRepository.findById(taskData.reviewerId);
          if (reviewer && reviewer.email) {
            const mailSubject = `Task Verification Required - ${inspectionData.maskId}`;
            const mailBody = `
              <h4>Dear ${reviewer.firstName ?? 'User'},</h4>
              <p>A task has been completed for Inspection ${inspectionData.maskId} and requires your verification.</p>
              <p><strong>Task:</strong> ${actionData.actionToBeTaken}</p>
              <p><strong>Action Taken:</strong> ${taskData.actionTaken ?? 'Not specified'}</p>
              <p>Please verify the task completion and update the status accordingly.</p>
              <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
            `;
            await this.sqsService.sendMessage(reviewer, mailSubject, mailBody);
          }
        }
        break;
      }

      case 'reperform_task': {
        const actions: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'verify_task',
          actionToBeTaken: actionData.actionToBeTaken,
          actionTaken: taskData.actionTaken ?? '',
          evidence: taskData.evidence ?? [],
          uploads: actionData.uploads,
          description: actionData.description,
          maskId: inspectionData.maskId,
          trackId: actionData.trackId,
          sequence: actionData.sequence,
          prefix: actionData.prefix,
          applicationId: inspectionData.id,
          objectId: inspectionData.id,
          submittedById: user.id,
          assignedToId: [taskData.reviewerId ?? ''], // Using the assigned by ID as reviewer
          submitURL: '/inspection-task-submit',
          dueDate: inspectionData.dueDate,
          status: 'Initiated',
          serviceId: service.id,
          counter: actionData.counter ?? 0
        };

        await this.actionRepository.updateById(actionId, {actionTaken: taskData.actionTaken ?? '', evidence: taskData.evidence ?? []});
        // Insert into actionRepository
        await this.actionRepository.create(actions);

        // Send notification to the reviewer
        if (taskData.reviewerId) {
          const reviewer = await this.userRepository.findById(taskData.reviewerId);
          if (reviewer && reviewer.email) {
            const mailSubject = `Task Re-verification Required - ${inspectionData.maskId}`;
            const mailBody = `
              <h4>Dear ${reviewer.firstName ?? 'User'},</h4>
              <p>A task has been re-performed for Inspection ${inspectionData.maskId} and requires your verification.</p>
              <p><strong>Task:</strong> ${actionData.actionToBeTaken}</p>
              <p><strong>Action Taken:</strong> ${taskData.actionTaken ?? 'Not specified'}</p>
              <p>Please verify the task completion and update the status accordingly.</p>
              <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
            `;
            await this.sqsService.sendMessage(reviewer, mailSubject, mailBody);
          }
        }
        break;
      }

      case 'verify_task': {
        // Process the task verification
        if (!taskData.status || (taskData.status !== 'Returned' && taskData.status !== 'Completed')) {
          throw new Error('Method Type Status not allowed')
        }

        switch (taskData.status) {
          case 'Returned': {
            // For Returned status, use the original assignedToId from the action data
            const actions: Partial<Action> = {
              application: SERVICE_NAME,
              actionType: 'reperform_task',
              actionToBeTaken: actionData.actionToBeTaken,
              actionTaken: actionData.actionTaken,
              uploads: actionData.uploads,
              evidence: actionData.evidence,
              description: actionData.description,
              comments: taskData.comments ?? '',
              maskId: inspectionData.maskId,
              trackId: actionData.trackId, // Generate unique id
              sequence: `${parseInt(actionData.sequence ?? '0') + 1}`,
              prefix: 'INS-TASK',
              applicationId: inspectionData.id,
              dueDate: inspectionData.dueDate,
              objectId: inspectionData.id,
              submittedById: user.id,
              assignedToId: actionData.assignedToId, // Using the original assignedToId
              submitURL: '/inspection-task-submit',
              status: 'Initiated',
              serviceId: service.id,
              counter: actionData.counter ?? 0
            };


            // Insert into actionRepository
            await this.actionRepository.create(actions);

            // Send notification to the original assignee
            if (actionData.assignedToId && actionData.assignedToId.length > 0) {
              const assigneeId = actionData.assignedToId[0];
              const assignee = await this.userRepository.findById(assigneeId);
              if (assignee && assignee.email) {
                const mailSubject = `Task Returned - ${inspectionData.maskId}`;
                const mailBody = `
                  <h4>Dear ${assignee.firstName ?? 'User'},</h4>
                  <p>Your task for Inspection ${inspectionData.maskId} has been reviewed and returned for further action.</p>
                  <p><strong>Task:</strong> ${actionData.actionToBeTaken}</p>
                  <p><strong>Comments:</strong> ${taskData.comments ?? 'No comments provided'}</p>
                  <p>Please address the comments and resubmit the task.</p>
                  <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
                `;
                await this.sqsService.sendMessage(assignee, mailSubject, mailBody);
              }
            }
            break;
          }

          case 'Completed': {
            // Check if there are any pending actions for this inspection

            // Send notification to the original assignee
            if (actionData.assignedToId && actionData.assignedToId.length > 0) {
              const assigneeId = actionData.assignedToId[0];
              const assignee = await this.userRepository.findById(assigneeId);
              if (assignee && assignee.email) {
                const mailSubject = `Task Completed - ${inspectionData.maskId}`;
                const mailBody = `
                  <h4>Dear ${assignee.firstName ?? 'User'},</h4>
                  <p>Your task for Inspection ${inspectionData.maskId} has been verified and marked as completed.</p>
                  <p><strong>Task:</strong> ${actionData.actionToBeTaken}</p>
                  <p>Thank you for your contribution to this inspection.</p>
                  <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
                `;
                await this.sqsService.sendMessage(assignee, mailSubject, mailBody);
              }
            }

            // Also notify the inspection creator
            if (inspectionData.assignedById) {
              const creator = await this.userRepository.findById(inspectionData.assignedById);
              if (creator && creator.email) {
                const mailSubject = `Task Completed - ${inspectionData.maskId}`;
                const mailBody = `
                  <h4>Dear ${creator.firstName ?? 'User'},</h4>
                  <p>A task for Inspection ${inspectionData.maskId} has been completed and verified.</p>
                  <p><strong>Task:</strong> ${actionData.actionToBeTaken}</p>
                  <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
                `;
                await this.sqsService.sendMessage(creator, mailSubject, mailBody);
              }
            }

            // status = pendingActions.count > 0 ? 'Completed with Actions' : 'Completed without Actions';
            break;
          }

          default:
            throw new Error('Method Type Status not allowed');
        }
        break;
      }

      default:
        throw new Error('Action type not allowed');
    }

    await this.actionRepository.updateById(actionId, {status: 'Completed', comments: taskData.comments ?? ''});

    // Update inspection with valid properties
    // const updateData: Partial<Inspection> = {

    // };

    // // Only add reviewerComments if it exists in the model
    // if (reviewerComments) {
    //   // Store it in the value field as a custom property
    //   updateData.value = {
    //     ...inspectionData.value,
    //     reviewerComments: reviewerComments
    //   };
    // }

    // await this.inspectionRepository.updateById(actionData.applicationId, updateData);
  }



  @patch('/inspection-checklist-submit/{actionId}')
  @response(204, {
    description: 'Inspection PATCH success',
  })
  async submitByActionId(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              checklist: {
                type: 'array',
                items: {
                  type: 'object'
                }
              },
              postActions: {
                type: 'array',
                items: {
                  type: 'object'
                }
              }
            }
          }
        },
      },
    })
    requestData: RequestData
  ): Promise<void> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})
    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const inspectionData = await this.inspectionRepository.findById(actionData.applicationId)
    if (!inspectionData) {
      throw new Error('No Inspection Data Found')
    }

    const checklistData = requestData.checklist as ChecklistItem[];
    // Store the checklist data
    inspectionData.value = checklistData;
    inspectionData.inspectionActions = requestData.postActions;

    // Track if any actions are created
    let actionsCreated = 0;
    let counter = 0;

    // Process checklist items with "No" responses
    if (checklistData && Array.isArray(checklistData)) {
      for (const item of checklistData) {
        // Handle checkpoint-group
        if (item.type === 'checkpoint-group' && item.data?.checkpoints) {
          for (const checkpoint of item.data.checkpoints) {
            if (
              checkpoint.selected === 'No' &&
              checkpoint.actionToBeTaken &&
              checkpoint.assignee
            ) {
              const action: Partial<Action> = {
                application: SERVICE_NAME,
                actionType: 'perform_task',
                actionToBeTaken: checkpoint.actionToBeTaken,
                description: checkpoint.text,
                maskId: inspectionData.maskId,
                trackId: uuidv4(),
                sequence: '1',
                prefix: 'INS-CPG',
                applicationId: inspectionData.id,
                objectId: inspectionData.id,
                dueDate: checkpoint.dueDate ?? undefined,
                submittedById: user.id ?? '',
                assignedToId: [checkpoint.assignee],
                uploads: checkpoint.uploads ?? [],
                submitURL: '/inspection-task-submit',
                status: 'Initiated',
                serviceId: service.id,
                counter: counter++
              };

              await this.actionRepository.create(action);
              actionsCreated++;

              const assignee = await this.userRepository.findById(checkpoint.assignee);
              if (assignee?.email) {
                const mailSubject = `New Checkpoint Action Required - ${inspectionData.maskId}`;
                const mailBody = `
              <h4>Dear ${assignee.firstName ?? 'User'},</h4>
              <p>A new checkpoint action has been assigned to you from Inspection ${inspectionData.maskId}.</p>
              <p><strong>Checkpoint:</strong> ${checkpoint.text}</p>
              <p><strong>Action to be taken:</strong> ${checkpoint.actionToBeTaken}</p>
              <p><strong>Due Date:</strong> ${checkpoint.dueDate ? new Date(checkpoint.dueDate).toLocaleDateString() : 'Not specified'}</p>
              <p><i>This email is an automated notification. Please do not reply.</i></p>
            `;
                await this.sqsService.sendMessage(assignee, mailSubject, mailBody);
              }
            }
          }
        }

        // Handle standalone checkpoint
        if (item.type === 'checkpoint' && item.data?.selected === 'No' &&
          item.data.actionToBeTaken && item.data.assignee) {
          const cp = item.data;

          if (cp.assignee) {
            const action: Partial<Action> = {
              application: SERVICE_NAME,
              actionType: 'perform_task',
              actionToBeTaken: cp.actionToBeTaken,
              description: cp.text,
              maskId: inspectionData.maskId,
              trackId: uuidv4(),
              sequence: '1',
              prefix: 'INS-CP',
              applicationId: inspectionData.id,
              objectId: inspectionData.id,
              dueDate: cp.dueDate ?? undefined,
              submittedById: user.id ?? '',
              assignedToId: [cp.assignee],
              uploads: cp.uploads ?? [],
              submitURL: '/inspection-task-submit',
              status: 'Initiated',
              serviceId: service.id,
              counter: counter++
            };

            await this.actionRepository.create(action);
            actionsCreated++;
          }

          const assignee = await this.userRepository.findById(cp.assignee);
          if (assignee?.email) {
            const mailSubject = `New Standalone Checkpoint Action - ${inspectionData.maskId}`;
            const mailBody = `
          <h4>Dear ${assignee.firstName ?? 'User'},</h4>
          <p>A new action has been assigned to you from Inspection ${inspectionData.maskId}.</p>
          <p><strong>Checkpoint:</strong> ${cp.text}</p>
          <p><strong>Action to be taken:</strong> ${cp.actionToBeTaken}</p>
          <p><strong>Due Date:</strong> ${cp.dueDate ? new Date(cp.dueDate).toLocaleDateString() : 'Not specified'}</p>
          <p><i>This email is an automated notification. Please do not reply.</i></p>
        `;
            await this.sqsService.sendMessage(assignee, mailSubject, mailBody);
          }
        }
      }
    }


    // Process post actions
    if (requestData.postActions && Array.isArray(requestData.postActions)) {
      for (const postAction of requestData.postActions) {
        if (postAction.actionToBeTaken && postAction.assignee) {
          const action: Partial<Action> = {
            application: SERVICE_NAME,
            actionType: 'perform_task',
            actionToBeTaken: postAction.actionToBeTaken,
            description: 'Post Inspection Action',
            maskId: inspectionData.maskId,
            trackId: uuidv4(), // Generate unique id
            sequence: '1',
            prefix: 'INS-PA',
            applicationId: inspectionData.id,
            objectId: inspectionData.id,
            dueDate: postAction.dueDate ?? undefined,
            submittedById: user.id ?? '',
            assignedToId: [postAction.assignee],
            uploads: postAction.uploads ?? [],
            submitURL: '/inspection-task-submit',
            status: 'Initiated',
            serviceId: service.id,
            counter: counter++
          };

          await this.actionRepository.create(action);
          actionsCreated++;

          // Send notification to the assignee
          const assignee = await this.userRepository.findById(postAction.assignee);
          if (assignee && assignee.email) {
            const mailSubject = `New Post-Inspection Action Required - ${inspectionData.maskId}`;
            const mailBody = `
              <h4>Dear ${assignee.firstName ?? 'User'},</h4>
              <p>A new post-inspection action has been assigned to you from Inspection ${inspectionData.maskId}.</p>
              <p><strong>Action to be taken:</strong> ${postAction.actionToBeTaken}</p>
              <p><strong>Due Date:</strong> ${postAction.dueDate ? new Date(postAction.dueDate).toLocaleDateString() : 'Not specified'}</p>
              <p>Please take the necessary action and update the status once completed.</p>
              <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
            `;
            await this.sqsService.sendMessage(assignee, mailSubject, mailBody);
          }
        }
      }
    }

    // Update inspection status based on whether actions were created
    const updateData: Partial<Inspection> = {
      actualCompletionDate: new Date().toISOString(),
      // Set status based on whether actions were created
      status: actionsCreated > 0 ? 'Completed with Actions' : 'Completed without Actions'
    };

    // Store information about actions in the value field
    const valueData = {
      ...inspectionData.value,
      hasActions: actionsCreated > 0,
      actionsCount: actionsCreated
    };

    updateData.value = valueData;

    await this.inspectionRepository.updateById(inspectionData.id, updateData);

    // Update the original action as completed
    await this.actionRepository.updateById(actionId, {status: 'Completed'});

    // Send notification to the inspection creator
    if (inspectionData.assignedById) {
      const creator = await this.userRepository.findById(inspectionData.assignedById);
      if (creator && creator.email) {
        const status = actionsCreated > 0 ? 'Completed with Actions' : 'Completed without Actions';
        const mailSubject = `Inspection Completed - ${inspectionData.maskId}`;
        const mailBody = `
          <h4>Dear ${creator.firstName ?? 'User'},</h4>
          <p>The inspection ${inspectionData.maskId} has been completed with status: ${status}.</p>
          ${actionsCreated > 0 ? `<p><strong>Number of actions created:</strong> ${actionsCreated}</p>` : '<p>No actions were required from this inspection.</p>'}
          <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
        `;
        await this.sqsService.sendMessage(creator, mailSubject, mailBody);
      }
    }

    // Send notification to the inspector
    if (inspectionData.inspectorId) {
      const inspector = await this.userRepository.findById(inspectionData.inspectorId);
      if (inspector && inspector.email && inspector.id !== user.id) {
        const status = actionsCreated > 0 ? 'Completed with Actions' : 'Completed without Actions';
        const mailSubject = `Inspection Completed - ${inspectionData.maskId}`;
        const mailBody = `
          <h4>Dear ${inspector.firstName ?? 'User'},</h4>
          <p>The inspection ${inspectionData.maskId} has been completed with status: ${status}.</p>
          ${actionsCreated > 0 ? `<p><strong>Number of actions created:</strong> ${actionsCreated}</p>` : '<p>No actions were required from this inspection.</p>'}
          <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
        `;
        await this.sqsService.sendMessage(inspector, mailSubject, mailBody);
      }
    }
  }

  @get('/inspections/count')
  @response(200, {
    description: 'Inspection model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Inspection) where?: Where<Inspection>,
  ): Promise<Count> {
    return this.inspectionRepository.count(where);
  }

  @get('/inspections')
  @response(200, {
    description: 'Array of Inspection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Inspection, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Inspection) filter?: Filter<Inspection>,
  ): Promise<Inspection[]> {
    const inspectionData = await this.inspectionRepository.find(filter);

    const modifiedInspection = await Promise.all(
      inspectionData.map(async (data) => {

        // Then fetch actions for each audit finding and include the auditFindings data in each action

        const totalActions = await this.actionRepository.find({where: {applicationId: data.id}});
        // Map each action to include the auditFinding data under applicationDetails


        // Filter actions to find those that are completed
        const completedActions = totalActions.filter(action => action.status === 'Completed');

        // Create an instance of ReportIncident with the desired properties
        const modifiedReport = new Inspection({
          ...data,

          totalActions: totalActions,
          completedActions: completedActions

        });

        return modifiedReport;
      })
    );

    return modifiedInspection;
  }


  @get('/my-scheduled-inspections')
  @response(200, {
    description: 'Array of Inspection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Inspection, {includeRelations: true}),
        },
      },
    },
  })
  async findInspections(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Inspection) filter?: Filter<Inspection>,
  ): Promise<Inspection[]> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }

    const filterWithAssignedBy: Filter<Inspection> = {
      ...filter,
      where: {
        ...filter?.where,
        assignedById: user.id
      }
    };

    const inspectionData = await this.inspectionRepository.find(filterWithAssignedBy);

    const modifiedInspection = await Promise.all(
      inspectionData.map(async (data) => {

        // Then fetch actions for each audit finding and include the auditFindings data in each action

        const totalActions = await this.actionRepository.find({where: {applicationId: data.id}});
        // Map each action to include the auditFinding data under applicationDetails


        // Filter actions to find those that are completed
        const completedActions = totalActions.filter(action => action.status === 'Completed');

        // Create an instance of ReportIncident with the desired properties
        const modifiedReport = new Inspection({
          ...data,

          totalActions: totalActions,
          completedActions: completedActions

        });

        return modifiedReport;
      })
    );

    return modifiedInspection;
  }

  @get('/my-inspections')
  @response(200, {
    description: 'Array of Inspection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Inspection, {includeRelations: true}),
        },
      },
    },
  })
  async findInspectedInspection(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Inspection) filter?: Filter<Inspection>,
  ): Promise<Inspection[]> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }

    const filterWithAssignedBy: Filter<Inspection> = {
      ...filter,
      where: {
        ...filter?.where,
        inspectorId: user.id
      }
    };

    const inspectionData = await this.inspectionRepository.find(filterWithAssignedBy);

    const modifiedInspection = await Promise.all(
      inspectionData.map(async (data) => {

        // Then fetch actions for each audit finding and include the auditFindings data in each action

        const totalActions = await this.actionRepository.find({where: {applicationId: data.id}});
        // Map each action to include the auditFinding data under applicationDetails


        // Filter actions to find those that are completed
        const completedActions = totalActions.filter(action => action.status === 'Completed');

        // Create an instance of ReportIncident with the desired properties
        const modifiedReport = new Inspection({
          ...data,

          totalActions: totalActions,
          completedActions: completedActions

        });

        return modifiedReport;
      })
    );

    return modifiedInspection;
  }

  @patch('/inspections')
  @response(200, {
    description: 'Inspection PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, {partial: true}),
        },
      },
    })
    inspection: Inspection,
    @param.where(Inspection) where?: Where<Inspection>,
  ): Promise<Count> {
    return this.inspectionRepository.updateAll(inspection, where);
  }

  @get('/inspections/{id}')
  @response(200, {
    description: 'Inspection model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Inspection, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Inspection, {exclude: 'where'}) filter?: FilterExcludingWhere<Inspection>
  ): Promise<Inspection> {
    return this.inspectionRepository.findById(id, filter);
  }

  @patch('/inspections/{id}')
  @response(204, {
    description: 'Inspection PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, {partial: true}),
        },
      },
    })
    inspection: Inspection,
  ): Promise<void> {
    await this.inspectionRepository.updateById(id, inspection);
  }

  @put('/inspections/{id}')
  @response(204, {
    description: 'Inspection PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() inspection: Inspection,
  ): Promise<void> {
    await this.inspectionRepository.replaceById(id, inspection);
  }

  @del('/inspections/{id}')
  @response(204, {
    description: 'Inspection DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.inspectionRepository.deleteById(id);
  }
}



