import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  KnowledgeArea,
  KnowledgeTopic,
} from '../models';
import {KnowledgeAreaRepository} from '../repositories';

export class KnowledgeAreaKnowledgeTopicController {
  constructor(
    @repository(KnowledgeAreaRepository) protected knowledgeAreaRepository: KnowledgeAreaRepository,
  ) { }

  @get('/knowledge-areas/{id}/knowledge-topics', {
    responses: {
      '200': {
        description: 'Array of KnowledgeArea has many KnowledgeTopic',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(KnowledgeTopic)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<KnowledgeTopic>,
  ): Promise<KnowledgeTopic[]> {
    return this.knowledgeAreaRepository.knowledgeTopics(id).find(filter);
  }

  @post('/knowledge-areas/{id}/knowledge-topics', {
    responses: {
      '200': {
        description: 'KnowledgeArea model instance',
        content: {'application/json': {schema: getModelSchemaRef(KnowledgeTopic)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof KnowledgeArea.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeTopic, {
            title: 'NewKnowledgeTopicInKnowledgeArea',
            exclude: ['id'],
            optional: ['knowledgeAreaId']
          }),
        },
      },
    }) knowledgeTopic: Omit<KnowledgeTopic, 'id'>,
  ): Promise<KnowledgeTopic> {
    return this.knowledgeAreaRepository.knowledgeTopics(id).create(knowledgeTopic);
  }

  @patch('/knowledge-areas/{id}/knowledge-topics', {
    responses: {
      '200': {
        description: 'KnowledgeArea.KnowledgeTopic PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeTopic, {partial: true}),
        },
      },
    })
    knowledgeTopic: Partial<KnowledgeTopic>,
    @param.query.object('where', getWhereSchemaFor(KnowledgeTopic)) where?: Where<KnowledgeTopic>,
  ): Promise<Count> {
    return this.knowledgeAreaRepository.knowledgeTopics(id).patch(knowledgeTopic, where);
  }

  @del('/knowledge-areas/{id}/knowledge-topics', {
    responses: {
      '200': {
        description: 'KnowledgeArea.KnowledgeTopic DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(KnowledgeTopic)) where?: Where<KnowledgeTopic>,
  ): Promise<Count> {
    return this.knowledgeAreaRepository.knowledgeTopics(id).delete(where);
  }
}
