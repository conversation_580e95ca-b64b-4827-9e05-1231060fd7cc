import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {KnowledgeArea} from '../models';
import {KnowledgeAreaRepository} from '../repositories';

export class KnowledgeAreaController {
  constructor(
    @repository(KnowledgeAreaRepository)
    public knowledgeAreaRepository : KnowledgeAreaRepository,
  ) {}

  @post('/knowledge-areas')
  @response(200, {
    description: 'KnowledgeArea model instance',
    content: {'application/json': {schema: getModelSchemaRef(KnowledgeArea)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeArea, {
            title: 'NewKnowledgeArea',
            exclude: ['id'],
          }),
        },
      },
    })
    knowledgeArea: Omit<KnowledgeArea, 'id'>,
  ): Promise<KnowledgeArea> {
    return this.knowledgeAreaRepository.create(knowledgeArea);
  }

  @get('/knowledge-areas/count')
  @response(200, {
    description: 'KnowledgeArea model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(KnowledgeArea) where?: Where<KnowledgeArea>,
  ): Promise<Count> {
    return this.knowledgeAreaRepository.count(where);
  }

  @get('/knowledge-areas')
  @response(200, {
    description: 'Array of KnowledgeArea model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(KnowledgeArea, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(KnowledgeArea) filter?: Filter<KnowledgeArea>,
  ): Promise<KnowledgeArea[]> {
    return this.knowledgeAreaRepository.find(filter);
  }

  @patch('/knowledge-areas')
  @response(200, {
    description: 'KnowledgeArea PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeArea, {partial: true}),
        },
      },
    })
    knowledgeArea: KnowledgeArea,
    @param.where(KnowledgeArea) where?: Where<KnowledgeArea>,
  ): Promise<Count> {
    return this.knowledgeAreaRepository.updateAll(knowledgeArea, where);
  }

  @get('/knowledge-areas/{id}')
  @response(200, {
    description: 'KnowledgeArea model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(KnowledgeArea, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(KnowledgeArea, {exclude: 'where'}) filter?: FilterExcludingWhere<KnowledgeArea>
  ): Promise<KnowledgeArea> {
    return this.knowledgeAreaRepository.findById(id, filter);
  }

  @patch('/knowledge-areas/{id}')
  @response(204, {
    description: 'KnowledgeArea PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeArea, {partial: true}),
        },
      },
    })
    knowledgeArea: KnowledgeArea,
  ): Promise<void> {
    await this.knowledgeAreaRepository.updateById(id, knowledgeArea);
  }

  @put('/knowledge-areas/{id}')
  @response(204, {
    description: 'KnowledgeArea PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() knowledgeArea: KnowledgeArea,
  ): Promise<void> {
    await this.knowledgeAreaRepository.replaceById(id, knowledgeArea);
  }

  @del('/knowledge-areas/{id}')
  @response(204, {
    description: 'KnowledgeArea DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.knowledgeAreaRepository.deleteById(id);
  }
}
