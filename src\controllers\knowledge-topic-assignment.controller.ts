import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  requestBody,
  response
} from '@loopback/rest';
import {KnowledgeTopicAssignment, User} from '../models';
import {KnowledgeTopicAssignmentRepository, UserRepository} from '../repositories';

export class KnowledgeTopicAssignmentController {
  constructor(
    @repository(KnowledgeTopicAssignmentRepository)
    public knowledgeTopicAssignmentRepository: KnowledgeTopicAssignmentRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @post('/knowledge-topic-assignments')
  @response(200, {
    description: 'KnowledgeTopicAssignment model instance',
    content: {'application/json': {schema: getModelSchemaRef(KnowledgeTopicAssignment)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeTopicAssignment, {
            title: 'NewKnowledgeTopicAssignment',
            exclude: ['id'],
          }),
        },
      },
    })
    knowledgeTopicAssignment: Omit<KnowledgeTopicAssignment, 'id'>,
  ): Promise<KnowledgeTopicAssignment> {
    return this.knowledgeTopicAssignmentRepository.create(knowledgeTopicAssignment);
  }

  @post('/knowledge-topic-assignments/bulk-assign')
  @response(200, {
    description: 'Bulk assign knowledge topics to organizational criteria',
    content: {'application/json': {schema: {type: 'object'}}},
  })
  async bulkAssign(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              knowledgeTopicIds: {
                type: 'array',
                items: {type: 'string'},
              },
              unitIds: {
                type: 'array',
                items: {type: 'string'},
              },
              departmentIds: {
                type: 'array',
                items: {type: 'string'},
              },
              divisionIds: {
                type: 'array',
                items: {type: 'string'},
              },
              subDivisionIds: {
                type: 'array',
                items: {type: 'string'},
              },
              categoryIds: {
                type: 'array',
                items: {type: 'string'},
              },
              gradeIds: {
                type: 'array',
                items: {type: 'string'},
              },
              designationIds: {
                type: 'array',
                items: {type: 'string'},
              },
              assignedBy: {type: 'string'},
            },
            required: ['knowledgeTopicIds'],
          },
        },
      },
    })
    assignmentData: {
      knowledgeTopicIds: string[];
      unitIds?: string[];
      departmentIds?: string[];
      divisionIds?: string[];
      subDivisionIds?: string[];
      categoryIds?: string[];
      gradeIds?: string[];
      designationIds?: string[];
      assignedBy?: string;
    },
  ): Promise<{success: boolean; assignmentsCreated: number; message: string}> {
    // Use the assignmentType type from the model
    type AssignmentType = 'UNIT' | 'DEPARTMENT' | 'DIVISION' | 'SUB_DIVISION' | 'CATEGORY' | 'GRADE' | 'DESIGNATION' | 'COMBINED' | undefined;

    type AssignmentData = {
      knowledgeTopicId: string;
      assignmentType: AssignmentType;
      unitIds: string[];
      departmentIds: string[];
      divisionIds: string[];
      subDivisionIds: string[];
      categoryIds: string[];
      gradeIds: string[];
      designationIds: string[];
      assignedBy?: string;
      active: boolean;
    };

    // Use the model's data interface for assignment objects
    const assignments: Partial<KnowledgeTopicAssignment>[] = [];

    for (const knowledgeTopicId of assignmentData.knowledgeTopicIds) {
      const assignment: Partial<KnowledgeTopicAssignment> = {
        knowledgeTopicId,
        assignmentType: 'COMBINED',
        unitIds: assignmentData.unitIds || [],
        departmentIds: assignmentData.departmentIds || [],
        divisionIds: assignmentData.divisionIds || [],
        subDivisionIds: assignmentData.subDivisionIds || [],
        categoryIds: assignmentData.categoryIds || [],
        gradeIds: assignmentData.gradeIds || [],
        designationIds: assignmentData.designationIds || [],
        assignedBy: assignmentData.assignedBy,
        active: true,
      };
      assignments.push(assignment);
    }

    const createdAssignments = await this.knowledgeTopicAssignmentRepository.createAll(assignments);

    return {
      success: true,
      assignmentsCreated: createdAssignments.length,
      message: `Successfully created ${createdAssignments.length} knowledge topic assignments`,
    };
  }

  @post('/knowledge-topic-assignments/bulk-unassign')
  @response(200, {
    description: 'Bulk unassign knowledge topics from organizational criteria',
    content: {'application/json': {schema: {type: 'object'}}},
  })
  async bulkUnassign(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              knowledgeTopicIds: {
                type: 'array',
                items: {type: 'string'},
              },
              unitIds: {
                type: 'array',
                items: {type: 'string'},
              },
              departmentIds: {
                type: 'array',
                items: {type: 'string'},
              },
              divisionIds: {
                type: 'array',
                items: {type: 'string'},
              },
              subDivisionIds: {
                type: 'array',
                items: {type: 'string'},
              },
              categoryIds: {
                type: 'array',
                items: {type: 'string'},
              },
              gradeIds: {
                type: 'array',
                items: {type: 'string'},
              },
              designationIds: {
                type: 'array',
                items: {type: 'string'},
              },
            },
            required: ['knowledgeTopicIds'],
          },
        },
      },
    })
    unassignmentData: {
      knowledgeTopicIds: string[];
      unitIds?: string[];
      departmentIds?: string[];
      divisionIds?: string[];
      subDivisionIds?: string[];
      categoryIds?: string[];
      gradeIds?: string[];
      designationIds?: string[];
    },
  ): Promise<{success: boolean; assignmentsRemoved: number; message: string}> {
    const whereConditions: Where<KnowledgeTopicAssignment> = {
      knowledgeTopicId: {inq: unassignmentData.knowledgeTopicIds},
    };

    // Build complex where condition based on provided criteria
    const orConditions: any[] = [];

    if (unassignmentData.unitIds?.length) {
      orConditions.push({unitIds: {inq: unassignmentData.unitIds}});
    }
    if (unassignmentData.departmentIds?.length) {
      orConditions.push({departmentIds: {inq: unassignmentData.departmentIds}});
    }
    if (unassignmentData.divisionIds?.length) {
      orConditions.push({divisionIds: {inq: unassignmentData.divisionIds}});
    }
    if (unassignmentData.subDivisionIds?.length) {
      orConditions.push({subDivisionIds: {inq: unassignmentData.subDivisionIds}});
    }
    if (unassignmentData.categoryIds?.length) {
      orConditions.push({categoryIds: {inq: unassignmentData.categoryIds}});
    }
    if (unassignmentData.gradeIds?.length) {
      orConditions.push({gradeIds: {inq: unassignmentData.gradeIds}});
    }
    if (unassignmentData.designationIds?.length) {
      orConditions.push({designationIds: {inq: unassignmentData.designationIds}});
    }

    let finalWhere: Where<KnowledgeTopicAssignment> = whereConditions;
    if (orConditions.length > 0) {
      finalWhere = {
        and: [
          whereConditions,
          {or: orConditions}
        ]
      };
    }

    const deletedCount = await this.knowledgeTopicAssignmentRepository.deleteAll(finalWhere);

    return {
      success: true,
      assignmentsRemoved: deletedCount.count,
      message: `Successfully removed ${deletedCount.count} knowledge topic assignments`,
    };
  }

  @get('/knowledge-topic-assignments/count')
  @response(200, {
    description: 'KnowledgeTopicAssignment model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(KnowledgeTopicAssignment) where?: Where<KnowledgeTopicAssignment>,
  ): Promise<Count> {
    return this.knowledgeTopicAssignmentRepository.count(where);
  }

  @get('/knowledge-topic-assignments')
  @response(200, {
    description: 'Array of KnowledgeTopicAssignment model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(KnowledgeTopicAssignment, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(KnowledgeTopicAssignment) filter?: Filter<KnowledgeTopicAssignment>,
  ): Promise<KnowledgeTopicAssignment[]> {
    return this.knowledgeTopicAssignmentRepository.find(filter);
  }

  @get('/knowledge-topic-assignments/{id}')
  @response(200, {
    description: 'KnowledgeTopicAssignment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(KnowledgeTopicAssignment, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(KnowledgeTopicAssignment, {exclude: 'where'}) filter?: FilterExcludingWhere<KnowledgeTopicAssignment>
  ): Promise<KnowledgeTopicAssignment> {
    return this.knowledgeTopicAssignmentRepository.findById(id, filter);
  }

  @patch('/knowledge-topic-assignments/{id}')
  @response(204, {
    description: 'KnowledgeTopicAssignment PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeTopicAssignment, {partial: true}),
        },
      },
    })
    knowledgeTopicAssignment: KnowledgeTopicAssignment,
  ): Promise<void> {
    await this.knowledgeTopicAssignmentRepository.updateById(id, knowledgeTopicAssignment);
  }

  @del('/knowledge-topic-assignments/{id}')
  @response(204, {
    description: 'KnowledgeTopicAssignment DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.knowledgeTopicAssignmentRepository.deleteById(id);
  }

  @get('/users/{userId}/assigned-knowledge-topics')
  @response(200, {
    description: 'Get knowledge topics assigned to a specific user based on their organizational attributes',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            user: {type: 'object'},
            assignedTopics: {
              type: 'array',
              items: {type: 'object'},
            },
            totalAssignments: {type: 'number'},
          },
        },
      },
    },
  })
  async getUserAssignedKnowledgeTopics(
    @param.path.string('userId') userId: string,
  ): Promise<{
    user: User;
    assignedTopics: any[];
    totalAssignments: number;
  }> {
    // Get user with all organizational attributes
    const user = await this.userRepository.findById(userId, {
      include: [
        {relation: 'department'},
        {relation: 'designation'},
        {relation: 'workingGroup'},
      ],
    });

    // Build query conditions based on user's organizational attributes
    const orConditions: any[] = [];

    // Check for unit assignments
    if (user.unitId) {
      orConditions.push({
        unitIds: {inq: [user.unitId]},
      });
    }

    // Check for department assignments
    if (user.departmentId) {
      orConditions.push({
        departmentIds: {inq: [user.departmentId]},
      });
    }

    // Check for division assignments
    if (user.divisionId) {
      orConditions.push({
        divisionIds: {inq: [user.divisionId]},
      });
    }

    // Check for sub-division assignments
    if (user.subDivisionId) {
      orConditions.push({
        subDivisionIds: {inq: [user.subDivisionId]},
      });
    }

    // Check for category assignments
    if (user.categoryId) {
      orConditions.push({
        categoryIds: {inq: [user.categoryId]},
      });
    }

    // Check for grade assignments
    if (user.gradeId) {
      orConditions.push({
        gradeIds: {inq: [user.gradeId]},
      });
    }

    // Check for designation assignments
    if (user.designationId) {
      orConditions.push({
        designationIds: {inq: [user.designationId]},
      });
    }

    let assignments: KnowledgeTopicAssignment[] = [];

    if (orConditions.length > 0) {
      assignments = await this.knowledgeTopicAssignmentRepository.find({
        where: {
          and: [
            {active: true},
            {or: orConditions},
          ],
        },
        include: [
          {
            relation: 'knowledgeTopic',
            scope: {
              include: [
                {relation: 'knowledgeArea'},
                {relation: 'knowledgeUnits'},
              ],
            },
          },
        ],
      });
    }

    return {
      user,
      assignedTopics: assignments,
      totalAssignments: assignments.length,
    };
  }
}
