import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  KnowledgeTopic,
  KnowledgeUnit,
} from '../models';
import {KnowledgeTopicRepository} from '../repositories';

export class KnowledgeTopicKnowledgeUnitController {
  constructor(
    @repository(KnowledgeTopicRepository) protected knowledgeTopicRepository: KnowledgeTopicRepository,
  ) { }

  @get('/knowledge-topics/{id}/knowledge-units', {
    responses: {
      '200': {
        description: 'Array of KnowledgeTopic has many KnowledgeUnit',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(KnowledgeUnit)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<KnowledgeUnit>,
  ): Promise<KnowledgeUnit[]> {
    return this.knowledgeTopicRepository.knowledgeUnits(id).find(filter);
  }

  @post('/knowledge-topics/{id}/knowledge-units', {
    responses: {
      '200': {
        description: 'KnowledgeTopic model instance',
        content: {'application/json': {schema: getModelSchemaRef(KnowledgeUnit)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof KnowledgeTopic.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeUnit, {
            title: 'NewKnowledgeUnitInKnowledgeTopic',
            exclude: ['id'],
            optional: ['knowledgeTopicId']
          }),
        },
      },
    }) knowledgeUnit: Omit<KnowledgeUnit, 'id'>,
  ): Promise<KnowledgeUnit> {
    return this.knowledgeTopicRepository.knowledgeUnits(id).create(knowledgeUnit);
  }

  @patch('/knowledge-topics/{id}/knowledge-units', {
    responses: {
      '200': {
        description: 'KnowledgeTopic.KnowledgeUnit PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeUnit, {partial: true}),
        },
      },
    })
    knowledgeUnit: Partial<KnowledgeUnit>,
    @param.query.object('where', getWhereSchemaFor(KnowledgeUnit)) where?: Where<KnowledgeUnit>,
  ): Promise<Count> {
    return this.knowledgeTopicRepository.knowledgeUnits(id).patch(knowledgeUnit, where);
  }

  @del('/knowledge-topics/{id}/knowledge-units', {
    responses: {
      '200': {
        description: 'KnowledgeTopic.KnowledgeUnit DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(KnowledgeUnit)) where?: Where<KnowledgeUnit>,
  ): Promise<Count> {
    return this.knowledgeTopicRepository.knowledgeUnits(id).delete(where);
  }
}
