import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {KnowledgeTopic} from '../models';
import {KnowledgeTopicRepository} from '../repositories';

export class KnowledgeTopicController {
  constructor(
    @repository(KnowledgeTopicRepository)
    public knowledgeTopicRepository : KnowledgeTopicRepository,
  ) {}

  @post('/knowledge-topics')
  @response(200, {
    description: 'KnowledgeTopic model instance',
    content: {'application/json': {schema: getModelSchemaRef(KnowledgeTopic)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeTopic, {
            title: 'NewKnowledgeTopic',
            exclude: ['id'],
          }),
        },
      },
    })
    knowledgeTopic: Omit<KnowledgeTopic, 'id'>,
  ): Promise<KnowledgeTopic> {
    return this.knowledgeTopicRepository.create(knowledgeTopic);
  }

  @get('/knowledge-topics/count')
  @response(200, {
    description: 'KnowledgeTopic model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(KnowledgeTopic) where?: Where<KnowledgeTopic>,
  ): Promise<Count> {
    return this.knowledgeTopicRepository.count(where);
  }

  @get('/knowledge-topics')
  @response(200, {
    description: 'Array of KnowledgeTopic model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(KnowledgeTopic, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(KnowledgeTopic) filter?: Filter<KnowledgeTopic>,
  ): Promise<KnowledgeTopic[]> {
    return this.knowledgeTopicRepository.find(filter);
  }

  @patch('/knowledge-topics')
  @response(200, {
    description: 'KnowledgeTopic PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeTopic, {partial: true}),
        },
      },
    })
    knowledgeTopic: KnowledgeTopic,
    @param.where(KnowledgeTopic) where?: Where<KnowledgeTopic>,
  ): Promise<Count> {
    return this.knowledgeTopicRepository.updateAll(knowledgeTopic, where);
  }

  @get('/knowledge-topics/{id}')
  @response(200, {
    description: 'KnowledgeTopic model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(KnowledgeTopic, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(KnowledgeTopic, {exclude: 'where'}) filter?: FilterExcludingWhere<KnowledgeTopic>
  ): Promise<KnowledgeTopic> {
    return this.knowledgeTopicRepository.findById(id, filter);
  }

  @patch('/knowledge-topics/{id}')
  @response(204, {
    description: 'KnowledgeTopic PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeTopic, {partial: true}),
        },
      },
    })
    knowledgeTopic: KnowledgeTopic,
  ): Promise<void> {
    await this.knowledgeTopicRepository.updateById(id, knowledgeTopic);
  }

  @put('/knowledge-topics/{id}')
  @response(204, {
    description: 'KnowledgeTopic PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() knowledgeTopic: KnowledgeTopic,
  ): Promise<void> {
    await this.knowledgeTopicRepository.replaceById(id, knowledgeTopic);
  }

  @del('/knowledge-topics/{id}')
  @response(204, {
    description: 'KnowledgeTopic DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.knowledgeTopicRepository.deleteById(id);
  }
}
