import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {KnowledgeUnit} from '../models';
import {KnowledgeUnitRepository} from '../repositories';

export class KnowledgeUnitController {
  constructor(
    @repository(KnowledgeUnitRepository)
    public knowledgeUnitRepository : KnowledgeUnitRepository,
  ) {}

  @post('/knowledge-units')
  @response(200, {
    description: 'KnowledgeUnit model instance',
    content: {'application/json': {schema: getModelSchemaRef(KnowledgeUnit)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeUnit, {
            title: 'NewKnowledgeUnit',
            exclude: ['id'],
          }),
        },
      },
    })
    knowledgeUnit: Omit<KnowledgeUnit, 'id'>,
  ): Promise<KnowledgeUnit> {
    return this.knowledgeUnitRepository.create(knowledgeUnit);
  }

  @get('/knowledge-units/count')
  @response(200, {
    description: 'KnowledgeUnit model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(KnowledgeUnit) where?: Where<KnowledgeUnit>,
  ): Promise<Count> {
    return this.knowledgeUnitRepository.count(where);
  }

  @get('/knowledge-units')
  @response(200, {
    description: 'Array of KnowledgeUnit model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(KnowledgeUnit, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(KnowledgeUnit) filter?: Filter<KnowledgeUnit>,
  ): Promise<KnowledgeUnit[]> {
    return this.knowledgeUnitRepository.find(filter);
  }

  @patch('/knowledge-units')
  @response(200, {
    description: 'KnowledgeUnit PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeUnit, {partial: true}),
        },
      },
    })
    knowledgeUnit: KnowledgeUnit,
    @param.where(KnowledgeUnit) where?: Where<KnowledgeUnit>,
  ): Promise<Count> {
    return this.knowledgeUnitRepository.updateAll(knowledgeUnit, where);
  }

  @get('/knowledge-units/{id}')
  @response(200, {
    description: 'KnowledgeUnit model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(KnowledgeUnit, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(KnowledgeUnit, {exclude: 'where'}) filter?: FilterExcludingWhere<KnowledgeUnit>
  ): Promise<KnowledgeUnit> {
    return this.knowledgeUnitRepository.findById(id, filter);
  }

  @patch('/knowledge-units/{id}')
  @response(204, {
    description: 'KnowledgeUnit PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeUnit, {partial: true}),
        },
      },
    })
    knowledgeUnit: KnowledgeUnit,
  ): Promise<void> {
    await this.knowledgeUnitRepository.updateById(id, knowledgeUnit);
  }

  @put('/knowledge-units/{id}')
  @response(204, {
    description: 'KnowledgeUnit PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() knowledgeUnit: KnowledgeUnit,
  ): Promise<void> {
    await this.knowledgeUnitRepository.replaceById(id, knowledgeUnit);
  }

  @del('/knowledge-units/{id}')
  @response(204, {
    description: 'KnowledgeUnit DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.knowledgeUnitRepository.deleteById(id);
  }
}
