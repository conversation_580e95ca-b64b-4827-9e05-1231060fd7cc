import {Entity, model, property, belongsTo, hasMany} from '@loopback/repository';
import {Department} from './department.model';
import {SubDivision} from './sub-division.model';

@model()
export class Division extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => Department)
  departmentId: string;

  @hasMany(() => SubDivision)
  subDivisions: SubDivision[];

  constructor(data?: Partial<Division>) {
    super(data);
  }
}

export interface DivisionRelations {
  // describe navigational properties here
}

export type DivisionWithRelations = Division & DivisionRelations;
