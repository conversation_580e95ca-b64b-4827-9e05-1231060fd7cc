import {Entity, model, property} from '@loopback/repository';

@model()
export class DocumentUpdate extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  data?: string;

  @property({
    type: 'string',
  })
  reasonReview?: string;

  @property({
    type: 'string',
  })
  changes?: string;

  @property({
    type: 'string',
  })
  reasonChange?: string;

  @property({
    type: 'string',
  })
  initiatedBy?: string;

  @property({
    type: 'string',
  })
  approvedBy?: string;

  @property({
    type: 'string',
  })
  reference?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  files?: string[];

  @property({
    type: 'string',
  })
  documentId?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  constructor(data?: Partial<DocumentUpdate>) {
    super(data);
  }
}

export interface DocumentUpdateRelations {
  // describe navigational properties here
}

export type DocumentUpdateWithRelations = DocumentUpdate & DocumentUpdateRelations;
