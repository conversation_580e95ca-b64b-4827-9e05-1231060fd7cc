import {Entity, model, property} from '@loopback/repository';

@model()
export class DocumentUserAllocation extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  userId?: string;

  @property({
    type: 'string',
  })
  documentId?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  constructor(data?: Partial<DocumentUserAllocation>) {
    super(data);
  }
}

export interface DocumentUserAllocationRelations {
  // describe navigational properties here
}

export type DocumentUserAllocationWithRelations = DocumentUserAllocation & DocumentUserAllocationRelations;
