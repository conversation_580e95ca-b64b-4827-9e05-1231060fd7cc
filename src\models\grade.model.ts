import {Entity, model, property} from '@loopback/repository';

@model()
export class Grade extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  constructor(data?: Partial<Grade>) {
    super(data);
  }
}

export interface GradeRelations {
  // describe navigational properties here
}

export type GradeWithRelations = Grade & GradeRelations;
