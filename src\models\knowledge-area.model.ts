import {Entity, hasMany, model, property} from '@loopback/repository';
import {KnowledgeTopic} from './knowledge-topic.model';

@model()
export class KnowledgeArea extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',

  })
  name?: string;

  @property({
    type: 'string',

  })
  description?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @hasMany(() => KnowledgeTopic)
  knowledgeTopics: KnowledgeTopic[];

  constructor(data?: Partial<KnowledgeArea>) {
    super(data);
  }
}

export interface KnowledgeAreaRelations {
  // describe navigational properties here
}

export type KnowledgeAreaWithRelations = KnowledgeArea & KnowledgeAreaRelations;
