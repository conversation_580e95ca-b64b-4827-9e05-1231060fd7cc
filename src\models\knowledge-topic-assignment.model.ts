import {Entity, model, property, belongsTo} from '@loopback/repository';
import {KnowledgeTopic} from './knowledge-topic.model';
import {Unit} from './unit.model';
import {Department} from './department.model';
import {Division} from './division.model';
import {SubDivision} from './sub-division.model';
import {Category} from './category.model';
import {Grade} from './grade.model';
import {Designation} from './designation.model';

@model()
export class KnowledgeTopicAssignment extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  assignmentType?: 'UNIT' | 'DEPARTMENT' | 'DIVISION' | 'SUB_DIVISION' | 'CATEGORY' | 'GRADE' | 'DESIGNATION' | 'COMBINED';

  @property({
    type: 'array',
    itemType: 'string',
  })
  unitIds?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  departmentIds?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  divisionIds?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  subDivisionIds?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  categoryIds?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  gradeIds?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  designationIds?: string[];

  @property({
    type: 'boolean',
    default: true,
  })
  active?: boolean;

  @property({
    type: 'string',
  })
  assignedBy?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  assignedDate?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => KnowledgeTopic)
  knowledgeTopicId: string;

  constructor(data?: Partial<KnowledgeTopicAssignment>) {
    super(data);
  }
}

export interface KnowledgeTopicAssignmentRelations {
  // describe navigational properties here
}

export type KnowledgeTopicAssignmentWithRelations = KnowledgeTopicAssignment & KnowledgeTopicAssignmentRelations;
