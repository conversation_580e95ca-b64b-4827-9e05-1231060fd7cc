import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {KnowledgeArea} from './knowledge-area.model';
import {KnowledgeUnit} from './knowledge-unit.model';

@model()
export class KnowledgeTopic extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',

  })
  name?: string;

  @property({
    type: 'string',

  })
  description?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => KnowledgeArea)
  knowledgeAreaId: string;

  @hasMany(() => KnowledgeUnit)
  knowledgeUnits: KnowledgeUnit[];

  constructor(data?: Partial<KnowledgeTopic>) {
    super(data);
  }
}

export interface KnowledgeTopicRelations {
  // describe navigational properties here
}

export type KnowledgeTopicWithRelations = KnowledgeTopic & KnowledgeTopicRelations;
