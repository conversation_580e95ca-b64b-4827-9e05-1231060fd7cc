import {belongsTo, Entity, model, property} from '@loopback/repository';
import {KnowledgeTopic} from './knowledge-topic.model';

@model()
export class KnowledgeUnit extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',

  })
  name?: string;

  @property({
    type: 'string',

  })
  description?: string;

  @property({
    type: 'string',

  })
  category?: string;

  @property({
    type: 'any',

  })
  value?: any;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => KnowledgeTopic)
  knowledgeTopicId: string;

  constructor(data?: Partial<KnowledgeUnit>) {
    super(data);
  }
}

export interface KnowledgeUnitRelations {
  // describe navigational properties here
}

export type KnowledgeUnitWithRelations = KnowledgeUnit & KnowledgeUnitRelations;
