import {Entity, model, property} from '@loopback/repository';

@model()
export class ObservationReportUser extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  observationReportId?: string;

  @property({
    type: 'string',
  })
  userId?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;


  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  constructor(data?: Partial<ObservationReportUser>) {
    super(data);
  }
}

export interface ObservationReportUserRelations {
  // describe navigational properties here
}

export type ObservationReportUserWithRelations = ObservationReportUser & ObservationReportUserRelations;
