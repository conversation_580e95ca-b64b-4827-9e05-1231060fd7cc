import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Division} from './division.model';

@model()
export class SubDivision extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => Division)
  divisionId: string;

  constructor(data?: Partial<SubDivision>) {
    super(data);
  }
}

export interface SubDivisionRelations {
  // describe navigational properties here
}

export type SubDivisionWithRelations = SubDivision & SubDivisionRelations;
