import {belongsTo, Entity, hasMany, hasOne, model, property} from '@loopback/repository';
import {Category} from './category.model';
import {Department} from './department.model';
import {Designation} from './designation.model';
import {Division} from './division.model';
import {DocumentUserAllocation} from './document-user-allocation.model';
import {Document} from './document.model';
import {Grade} from './grade.model';
import {SubDivision} from './sub-division.model';
import {Unit} from './unit.model';
import {UserLocationRole} from './user-location-role.model';
import {UserLocation} from './user-location.model';
import {WorkingGroup} from './working-group.model';

@model({
  settings: {
    indexes: {
      uniqueEmail: {
        keys: {
          email: 1,
        },
        options: {
          unique: true,
        },
      },
    },
  },
})
export class User extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
  })
  firstName?: string;

  @property({
    type: 'string',
  })
  lastName?: string;

  @property({
    type: 'string',
  })
  signature?: string;

  @property({
    type: 'string',
  })
  email?: string;

  @property({
    type: 'string',
    required: true,
  })
  company: string;

  @property({
    type: 'string',

  })
  deviceToken?: string;

  @property({
    type: 'string',

  })
  arn?: string;

  @property({
    type: 'string',
  })
  type?: 'External' | 'Internal' | 'AD';

  @property({
    type: 'boolean',
    default: true,
  })
  status?: boolean;

  @property({
    type: 'array',
    itemType: 'string',
  })
  roles?: string[];

  @property({
    type: 'string',
  })
  country?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;
  @hasOne(() => UserLocation)
  userLocation: UserLocation;

  @hasMany(() => UserLocationRole)
  userLocationRoles: UserLocationRole[];

  @belongsTo(() => Department)
  departmentId: string;

  @belongsTo(() => Designation)
  designationId: string;

  @belongsTo(() => WorkingGroup)
  workingGroupId: string;

  @belongsTo(() => Division)
  divisionId?: string;

  @belongsTo(() => SubDivision)
  subDivisionId?: string;

  @belongsTo(() => Category)
  categoryId?: string;

  @belongsTo(() => Grade)
  gradeId?: string;

  @belongsTo(() => Unit)
  unitId?: string;

  @hasMany(() => Document, {through: {model: () => DocumentUserAllocation}})
  documents: Document[];

  constructor(data?: Partial<User>) {
    super(data);
  }
}

export interface UserRelations {
  // describe navigational properties here
}

export type UserWithRelations = User & UserRelations;
