import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository} from '@loopback/repository';
import {Division, DivisionRelations, Department, SubDivision} from '../models';
import {DepartmentRepository} from './department.repository';
import {SubDivisionRepository} from './sub-division.repository';

export class DivisionRepository extends DefaultCrudRepository<
  Division,
  typeof Division.prototype.id,
  DivisionRelations
> {

  public readonly department: BelongsToAccessor<Department, typeof Division.prototype.id>;

  public readonly subDivisions: HasManyRepositoryFactory<SubDivision, typeof Division.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource,
    @repository.getter('DepartmentRepository') protected departmentRepositoryGetter: Getter<DepartmentRepository>,
    @repository.getter('SubDivisionRepository') protected subDivisionRepositoryGetter: Getter<SubDivisionRepository>,
  ) {
    super(Division, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });

    this.subDivisions = this.createHasManyRepositoryFactoryFor('subDivisions', subDivisionRepositoryGetter);
    this.registerInclusionResolver('subDivisions', this.subDivisions.inclusionResolver);
    
    this.department = this.createBelongsToAccessorFor('department', departmentRepositoryGetter);
    this.registerInclusionResolver('department', this.department.inclusionResolver);
  }
}
