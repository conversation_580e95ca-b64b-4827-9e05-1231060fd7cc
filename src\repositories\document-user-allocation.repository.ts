import {inject} from '@loopback/core';
import {DefaultCrudRepository, juggler} from '@loopback/repository';
import {DocumentUserAllocation, DocumentUserAllocationRelations} from '../models';

export class DocumentUserAllocationRepository extends DefaultCrudRepository<
  DocumentUserAllocation,
  typeof DocumentUserAllocation.prototype.id,
  DocumentUserAllocationRelations
> {
  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource,
  ) {
    super(DocumentUserAllocation, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
