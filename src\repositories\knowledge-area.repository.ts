import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository} from '@loopback/repository';
import {KnowledgeArea, KnowledgeAreaRelations, KnowledgeTopic} from '../models';
import {KnowledgeTopicRepository} from './knowledge-topic.repository';

export class KnowledgeAreaRepository extends DefaultCrudRepository<
  KnowledgeArea,
  typeof KnowledgeArea.prototype.id,
  KnowledgeAreaRelations
> {

  public readonly knowledgeTopics: HasManyRepositoryFactory<KnowledgeTopic, typeof KnowledgeArea.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource,
    @repository.getter('KnowledgeTopicRepository') protected knowledgeTopicRepositoryGetter: Getter<KnowledgeTopicRepository>,
  ) {
    super(KnowledgeArea, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });

    this.knowledgeTopics = this.createHasManyRepositoryFactoryFor('knowledgeTopics', knowledgeTopicRepositoryGetter);
    this.registerInclusionResolver('knowledgeTopics', this.knowledgeTopics.inclusionResolver);
  }
}
