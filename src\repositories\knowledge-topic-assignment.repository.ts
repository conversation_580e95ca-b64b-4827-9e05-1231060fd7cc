import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, juggler, repository} from '@loopback/repository';
import {KnowledgeTopicAssignment, KnowledgeTopicAssignmentRelations, KnowledgeTopic} from '../models';
import {KnowledgeTopicRepository} from './knowledge-topic.repository';

export class KnowledgeTopicAssignmentRepository extends DefaultCrudRepository<
  KnowledgeTopicAssignment,
  typeof KnowledgeTopicAssignment.prototype.id,
  KnowledgeTopicAssignmentRelations
> {

  public readonly knowledgeTopic: BelongsToAccessor<KnowledgeTopic, typeof KnowledgeTopicAssignment.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource,
    @repository.getter('KnowledgeTopicRepository') protected knowledgeTopicRepositoryGetter: Getter<KnowledgeTopicRepository>,
  ) {
    super(KnowledgeTopicAssignment, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });

    this.knowledgeTopic = this.createBelongsToAccessorFor('knowledgeTopic', knowledgeTopicRepositoryGetter);
    this.registerInclusionResolver('knowledgeTopic', this.knowledgeTopic.inclusionResolver);
  }
}
