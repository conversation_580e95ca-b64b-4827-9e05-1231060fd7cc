import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory, juggler} from '@loopback/repository';
import {KnowledgeTopic, KnowledgeTopicRelations, KnowledgeArea, KnowledgeUnit} from '../models';
import {KnowledgeAreaRepository} from './knowledge-area.repository';
import {KnowledgeUnitRepository} from './knowledge-unit.repository';

export class KnowledgeTopicRepository extends DefaultCrudRepository<
  KnowledgeTopic,
  typeof KnowledgeTopic.prototype.id,
  KnowledgeTopicRelations
> {

  public readonly knowledgeArea: BelongsToAccessor<KnowledgeArea, typeof KnowledgeTopic.prototype.id>;

  public readonly knowledgeUnits: HasManyRepositoryFactory<KnowledgeUnit, typeof KnowledgeTopic.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, 
    @repository.getter('KnowledgeAreaRepository') protected knowledgeAreaRepositoryGetter: Getter<KnowledgeAreaRepository>, 
    @repository.getter('KnowledgeUnitRepository') protected knowledgeUnitRepositoryGetter: Getter<KnowledgeUnitRepository>,
  ) {
    super(KnowledgeTopic, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });

    this.knowledgeUnits = this.createHasManyRepositoryFactoryFor('knowledgeUnits', knowledgeUnitRepositoryGetter);
    this.registerInclusionResolver('knowledgeUnits', this.knowledgeUnits.inclusionResolver);
    
    this.knowledgeArea = this.createBelongsToAccessorFor('knowledgeArea', knowledgeAreaRepositoryGetter);
    this.registerInclusionResolver('knowledgeArea', this.knowledgeArea.inclusionResolver);
  }
}
