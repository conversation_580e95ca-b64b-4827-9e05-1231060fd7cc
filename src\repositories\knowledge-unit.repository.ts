import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, juggler} from '@loopback/repository';
import {KnowledgeUnit, KnowledgeUnitRelations, KnowledgeTopic} from '../models';
import {KnowledgeTopicRepository} from './knowledge-topic.repository';

export class KnowledgeUnitRepository extends DefaultCrudRepository<
  KnowledgeUnit,
  typeof KnowledgeUnit.prototype.id,
  KnowledgeUnitRelations
> {

  public readonly knowledgeTopic: BelongsToAccessor<KnowledgeTopic, typeof KnowledgeUnit.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, 
    @repository.getter('KnowledgeTopicRepository') protected knowledgeTopicRepositoryGetter: Getter<KnowledgeTopicRepository>,
  ) {
    super(KnowledgeUnit, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });

    this.knowledgeTopic = this.createBelongsToAccessorFor('knowledgeTopic', knowledgeTopicRepositoryGetter);
    this.registerInclusionResolver('knowledgeTopic', this.knowledgeTopic.inclusionResolver);
  }
}
