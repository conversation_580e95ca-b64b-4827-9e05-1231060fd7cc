import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository, HasManyThroughRepositoryFactory} from '@loopback/repository';
import {Action, LocationFive, LocationFour, LocationOne, LocationSix, LocationThree, LocationTwo, ObservationReport, ObservationReportRelations, User, ObservationReportUser} from '../models';
import {ActionRepository} from './action.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationSixRepository} from './location-six.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationTwoRepository} from './location-two.repository';
import {UserRepository} from './user.repository';
import {ObservationReportUserRepository} from './observation-report-user.repository';

export class ObservationReportRepository extends DefaultCrudRepository<
  ObservationReport,
  typeof ObservationReport.prototype.id,
  ObservationReportRelations
> {

  public readonly reporter: BelongsToAccessor<User, typeof ObservationReport.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof ObservationReport.prototype.id>;

  public readonly actionOwner: BelongsToAccessor<User, typeof ObservationReport.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof ObservationReport.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof ObservationReport.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof ObservationReport.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof ObservationReport.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof ObservationReport.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof ObservationReport.prototype.id>;

  public readonly observationActions: HasManyRepositoryFactory<Action, typeof ObservationReport.prototype.id>;

  public readonly multiActionOwners: HasManyThroughRepositoryFactory<User, typeof User.prototype.id,
          ObservationReportUser,
          typeof ObservationReport.prototype.id
        >;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>, @repository.getter('ActionRepository') protected actionRepositoryGetter: Getter<ActionRepository>, @repository.getter('ObservationReportUserRepository') protected observationReportUserRepositoryGetter: Getter<ObservationReportUserRepository>,
  ) {
    super(ObservationReport, dataSource);
    this.multiActionOwners = this.createHasManyThroughRepositoryFactoryFor('multiActionOwners', userRepositoryGetter, observationReportUserRepositoryGetter,);
    this.registerInclusionResolver('multiActionOwners', this.multiActionOwners.inclusionResolver);
    this.observationActions = this.createHasManyRepositoryFactoryFor('observationActions', actionRepositoryGetter,);
    this.registerInclusionResolver('observationActions', this.observationActions.inclusionResolver);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.actionOwner = this.createBelongsToAccessorFor('actionOwner', userRepositoryGetter,);
    this.registerInclusionResolver('actionOwner', this.actionOwner.inclusionResolver);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);
    this.reporter = this.createBelongsToAccessorFor('reporter', userRepositoryGetter,);
    this.registerInclusionResolver('reporter', this.reporter.inclusionResolver);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
