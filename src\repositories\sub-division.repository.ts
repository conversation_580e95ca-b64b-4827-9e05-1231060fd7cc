import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, juggler, repository} from '@loopback/repository';
import {SubDivision, SubDivisionRelations, Division} from '../models';
import {DivisionRepository} from './division.repository';

export class SubDivisionRepository extends DefaultCrudRepository<
  SubDivision,
  typeof SubDivision.prototype.id,
  SubDivisionRelations
> {

  public readonly division: BelongsToAccessor<Division, typeof SubDivision.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource,
    @repository.getter('DivisionRepository') protected divisionRepositoryGetter: Getter<DivisionRepository>,
  ) {
    super(SubDivision, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });

    this.division = this.createBelongsToAccessorFor('division', divisionRepositoryGetter);
    this.registerInclusionResolver('division', this.division.inclusionResolver);
  }
}
