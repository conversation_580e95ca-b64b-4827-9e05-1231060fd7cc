import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, HasOneRepositoryFactory, juggler, repository, HasManyThroughRepositoryFactory} from '@loopback/repository';
import {Department, Designation, User, UserLocation, UserLocationRole, UserRelations, WorkingGroup, Document, DocumentUserAllocation} from '../models';
import {DepartmentRepository} from './department.repository';
import {DesignationRepository} from './designation.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';
import {UserLocationRepository} from './user-location.repository';
import {WorkingGroupRepository} from './working-group.repository';
import {DocumentUserAllocationRepository} from './document-user-allocation.repository';
import {DocumentRepository} from './document.repository';

export class UserRepository extends DefaultCrudRepository<
  User,
  typeof User.prototype.id,
  UserRelations
> {

  public readonly userLocation: HasOneRepositoryFactory<UserLocation, typeof User.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof User.prototype.id>;

  public readonly department: BelongsToAccessor<Department, typeof User.prototype.id>;

  public readonly designation: BelongsToAccessor<Designation, typeof User.prototype.id>;

  public readonly workingGroup: BelongsToAccessor<WorkingGroup, typeof User.prototype.id>;

  public readonly documents: HasManyThroughRepositoryFactory<Document, typeof Document.prototype.id,
          DocumentUserAllocation,
          typeof User.prototype.id
        >;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('UserLocationRepository') protected userLocationRepositoryGetter: Getter<UserLocationRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>, @repository.getter('DepartmentRepository') protected departmentRepositoryGetter: Getter<DepartmentRepository>, @repository.getter('DesignationRepository') protected designationRepositoryGetter: Getter<DesignationRepository>, @repository.getter('WorkingGroupRepository') protected workingGroupRepositoryGetter: Getter<WorkingGroupRepository>, @repository.getter('DocumentUserAllocationRepository') protected documentUserAllocationRepositoryGetter: Getter<DocumentUserAllocationRepository>, @repository.getter('DocumentRepository') protected documentRepositoryGetter: Getter<DocumentRepository>,
  ) {
    super(User, dataSource);
    this.documents = this.createHasManyThroughRepositoryFactoryFor('documents', documentRepositoryGetter, documentUserAllocationRepositoryGetter,);
    this.registerInclusionResolver('documents', this.documents.inclusionResolver);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
    this.workingGroup = this.createBelongsToAccessorFor('workingGroup', workingGroupRepositoryGetter,);
    this.registerInclusionResolver('workingGroup', this.workingGroup.inclusionResolver);
    this.designation = this.createBelongsToAccessorFor('designation', designationRepositoryGetter,);
    this.registerInclusionResolver('designation', this.designation.inclusionResolver);
    this.department = this.createBelongsToAccessorFor('department', departmentRepositoryGetter,);
    this.registerInclusionResolver('department', this.department.inclusionResolver);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.userLocation = this.createHasOneRepositoryFactoryFor('userLocation', userLocationRepositoryGetter);
    this.registerInclusionResolver('userLocation', this.userLocation.inclusionResolver);
  }
}
