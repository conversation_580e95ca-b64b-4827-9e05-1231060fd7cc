export type Upload = string;

export interface Checkpoint {
  id: string;
  text: string;
  selected: string; // 'Yes' | 'No' | '' (can enforce union if needed)
  remarks?: string;
  actionToBeTaken?: string;
  dueDate?: string;
  assignee?: string;
  uploads?: Upload[];
}

export interface CheckpointGroup {
  id: string;
  type: 'checkpoint-group';
  position: number;
  data: {
    id: string;
    type: 'checkpoint-group';
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
    title: string;
    checkpoints: Checkpoint[];
    groupAnswer?: string;
    reason?: string;
  };
  validation?: {
    isValid: boolean;
    lastValidated: string;
  };
}

export interface SingleCheckpoint {
  id: string;
  type: 'checkpoint';
  position: number;
  data: Checkpoint & {
    id: string;
    type: 'checkpoint';
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
  };
  validation?: {
    isValid: boolean;
    lastValidated: string;
  };
}

export interface DateField {
  id: string;
  type: 'date';
  position: number;
  data: {
    id: string;
    type: 'date';
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
    label: string;
    selectedDate: string;
  };
  validation?: {
    isValid: boolean;
    lastValidated: string;
  };
}

export interface Header {
  id: string;
  type: 'header' | 'section-header';
  position: number;
  data: {
    id: string;
    type: 'header' | 'section-header';
    position: number;
    required?: boolean;
    allowFromLibrary?: boolean;
    text: string;
  };
  validation?: {
    isValid: boolean;
    lastValidated: string;
  };
}

export interface TextBody {
  id: string;
  type: 'text-body';
  position: number;
  data: {
    type: 'text-body';
    position: number;
    content: string;
  };
  validation?: {
    isValid: boolean;
    lastValidated: string;
  };
}

export interface Signature {
  id: string;
  type: 'sign';
  position: number;
  data: {
    id: string;
    type: 'sign';
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
    label: string;
    signature: string;
  };
  validation?: {
    isValid: boolean;
    lastValidated: string;
  };
}

export type ChecklistItem =
  | CheckpointGroup
  | SingleCheckpoint
  | DateField
  | Header
  | TextBody
  | Signature;
